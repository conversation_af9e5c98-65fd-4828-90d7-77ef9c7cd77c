# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Examples:
#
#   movies = Movie.create([{ name: "Star Wars" }, { name: "Lord of the Rings" }])
#   Character.c# db/seeds.rb

# Clear existing data
# 
# User.destroy_all
# Project.destroy_all
# NetworkConnection.destroy_all
# ConnectionRequest.destroy_all

# Create 10 testing users
# db/seeds.rb

# Clear existing data
# User.destroy_all
# Project.destroy_all
# NetworkConnection.destroy_all
# ConnectionRequest.destroy_all

# Create 10 testing users
# db/seeds.rb

# Clear existing data
# User.destroy_all
# Project.destroy_all
# NetworkConnection.destroy_all
# ConnectionRequest.destroy_all

# Create 10 testing users
10.times do |i|
  user = User.create!(
    email: "fifth#{i}@example.com",
    password: "password",
    password_confirmation: "password"
  )
  
  UserProfile.create!(
    user: user,
    first_name:["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"].sample,
    last_name: ["<PERSON>", "<PERSON> III", "<PERSON> Sr.", "Brown II", "Jones IV", "Garcia V", "<PERSON> Jr.", "<PERSON> III", "<PERSON> Sr.", "<PERSON> II"].sample,
    bio: [
      "Passionate developer with a love for creating innovative solutions.",
      "Experienced project manager with a knack for team leadership.",
      "Creative designer with a keen eye for detail and aesthetics.",
      "Data scientist with a strong background in statistical analysis.",
      "Marketing expert with a talent for crafting compelling campaigns.",
      "Seasoned entrepreneur with a track record of successful startups.",
      "Software engineer with a focus on scalable web applications.",
      "Product manager with a strategic approach to product development.",
      "UX/UI designer dedicated to enhancing user experiences.",
      "Business analyst with expertise in market research and data analysis."
    ].sample,
    location: ["Kosice", "Poprad", "Italy", "Germany", "Great Britain", "Moldova", "Romania", "Lipany"].sample
  )

  # Create associated projects for each user
  3.times do |j|
    category = Project.categories.keys.sample
    subcategory = Project::CATEGORIES[category.to_sym].sample
    Project.create!(
      user: user,
      summary: 
      [
        "Innovative app to streamline workflow and increase productivity.",
        "A cutting-edge platform for seamless team collaboration.",
        "Revolutionary tool for efficient project management.",
        "Next-gen solution for data-driven decision making.",
        "User-friendly interface for enhanced user experience.",
        "Robust system for secure data storage and retrieval.",
        "Advanced analytics tool for market trend analysis.",
        "Comprehensive suite for end-to-end project tracking.",
        "Scalable architecture for growing business needs.",
        "Intuitive design for effortless navigation and use."
      ].sample, 
      location: ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"].sample,
      category: category,
      subcategory: subcategory,
      summary_only: true,
      semi_public: true
    )
  end
end

# Create connection requests to user with id=1
main_user = User.find(1)
User.where.not(id: main_user.id).each do |user|
  ConnectionRequest.create!(
    inviter: user,
    invitee: main_user,
    status: 0, # Pending
    message: "Hello! I would like to connect with you to discuss potential collaboration opportunities. I have been following your work and I am impressed with your projects. Let's connect and explore how we can work together to achieve our goals. Looking forward to hearing from you soon. Best regards."
  )
end

AdminUser.create!(email: '<EMAIL>', password: 'password', password_confirmation: 'password') if Rails.env.development?

# Create subscription plans matching Stripe products
puts "Creating subscription plans..."
plans_data = [
  { name: "Free", tier: "free", interval: "one_time", price_cents: 0 },
  { name: "Premium Monthly", tier: "premium", interval: "month", price_cents: 999 },
  { name: "Premium Annual", tier: "premium", interval: "year", price_cents: 9999 },
  { name: "Premium Lifetime", tier: "premium", interval: "one_time", price_cents: 29999 },
  { name: "Pilot Monthly", tier: "pilot", interval: "month", price_cents: 0 },
  { name: "Pilot Annual", tier: "pilot", interval: "year", price_cents: 0 },
  { name: "Pilot Lifetime", tier: "pilot", interval: "one_time", price_cents: 0 }
]

plans_data.each do |plan_data|
  Plan.find_or_create_by!(name: plan_data[:name]) do |plan|
    plan.tier = plan_data[:tier]
    plan.interval = plan_data[:interval]
    plan.price_cents = plan_data[:price_cents]
    plan.active = true
    puts "Created plan: #{plan.name}"
  end
end

puts "Subscription plans created successfully!"