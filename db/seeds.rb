# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

puts "🌱 Seeding database for #{Rails.env} environment..."

# Load shared seeds that are needed in all environments
shared_seeds_path = Rails.root.join('db', 'seeds', 'shared.rb')
if File.exist?(shared_seeds_path)
  puts "Loading shared seeds..."
  load shared_seeds_path
end

# Load environment-specific seeds
environment_seeds_path = Rails.root.join('db', 'seeds', "#{Rails.env}.rb")
if File.exist?(environment_seeds_path)
  puts "Loading #{Rails.env} environment seeds..."
  load environment_seeds_path
else
  puts "No specific seeds found for #{Rails.env} environment"
end

puts "✅ Seeding completed for #{Rails.env} environment!"