# Production environment seeds - only essential data for production

puts "Creating production environment data..."

# In production, we typically only want to create essential system data
# No test users or sample data should be created here

# You might want to create essential admin users here if needed
# But be very careful about hardcoded credentials in production
# Consider using environment variables for sensitive data

# Example (commented out for security):
# if ENV['ADMIN_EMAIL'].present? && ENV['ADMIN_PASSWORD'].present?
#   AdminUser.find_or_create_by!(email: ENV['ADMIN_EMAIL']) do |admin|
#     admin.password = ENV['ADMIN_PASSWORD']
#     admin.password_confirmation = ENV['ADMIN_PASSWORD']
#     puts "  ✓ Created production admin user"
#   end
# end

puts "✅ Production environment seeding completed!"
puts "⚠️  Note: No test data created in production environment"
