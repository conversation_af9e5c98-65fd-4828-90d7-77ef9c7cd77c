# Shared seeds that are needed in all environments (development, test, production)

puts "Creating subscription plans..."

# Create subscription plans matching Stripe products
plans_data = [
  { name: "Free", tier: "free", interval: "one_time", price_cents: 0 },
  { name: "Premium Monthly", tier: "premium", interval: "month", price_cents: 999 },
  { name: "Premium Annual", tier: "premium", interval: "year", price_cents: 9999 },
  { name: "Premium Lifetime", tier: "premium", interval: "one_time", price_cents: 29999 },
  { name: "Pilot Monthly", tier: "pilot", interval: "month", price_cents: 0 },
  { name: "Pilot Annual", tier: "pilot", interval: "year", price_cents: 0 },
  { name: "Pilot Lifetime", tier: "pilot", interval: "one_time", price_cents: 0 }
]

plans_data.each do |plan_data|
  Plan.find_or_create_by!(name: plan_data[:name]) do |plan|
    plan.tier = plan_data[:tier]
    plan.interval = plan_data[:interval]
    plan.price_cents = plan_data[:price_cents]
    plan.active = true
    puts "  ✓ Created plan: #{plan.name}"
  end
end

puts "✅ Subscription plans created successfully!"
