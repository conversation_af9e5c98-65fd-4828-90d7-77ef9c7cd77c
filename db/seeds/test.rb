# Test environment seeds - minimal data needed for testing

puts "Creating test environment data..."

# Create a minimal admin user for testing
AdminUser.find_or_create_by!(email: '<EMAIL>') do |admin|
  admin.password = 'password'
  admin.password_confirmation = 'password'
  puts "  ✓ Created test admin user"
end

# Create a few test users for testing purposes
3.times do |i|
  email = "test-user#{i}@example.com"
  
  # Skip if user already exists
  next if User.exists?(email: email)
  
  user = User.create!(
    email: email,
    password: "password",
    password_confirmation: "password"
  )
  
  UserProfile.create!(
    user: user,
    first_name: "Test",
    last_name: "User#{i}",
    bio: "Test user for automated testing",
    location: "Test Location"
  )
  
  puts "  ✓ Created test user: #{user.email}"
end

puts "✅ Test environment data created successfully!"
