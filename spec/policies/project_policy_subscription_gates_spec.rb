# ABOUTME: Tests for subscription-based project policy gates for free users
# ABOUTME: Validates 14-day visibility, summary_only restrictions, and request access limitations

require 'rails_helper'

RSpec.describe ProjectPolicy, 'subscription gates', type: :policy do
  subject(:policy) { described_class.new(project, user: user) }
  
  let(:free_user) { create(:user) }
  let(:subscribed_user) { create(:user) }
  let(:project_owner) { create(:user) }
  let(:project) { create(:project, user: project_owner) }
  
  before do
    # Setup subscribed user with active subscription
    plan = create(:plan, tier: 'standard')
    create(:subscription, user: subscribed_user, plan: plan, status: 'active')
  end

  describe '#viewable_by_free_user?' do
    context 'when user has active subscription' do
      let(:user) { subscribed_user }
      
      it 'allows viewing any project regardless of age' do
        new_project = create(:project, created_at: 1.day.ago, first_published_at: 1.day.ago)
        new_policy = described_class.new(new_project, user: user)
        expect(new_policy.viewable_by_free_user?).to be true
      end
    end

    context 'when user is free (no subscription)' do
      let(:user) { free_user }
      
      context 'and project is older than 14 days' do
        let(:project) { create(:project, first_published_at: 15.days.ago) }
        
        it 'allows viewing' do
          expect(policy.viewable_by_free_user?).to be true
        end
      end
      
      context 'and project is newer than 14 days' do
        let(:project) { create(:project, first_published_at: 10.days.ago) }
        
        it 'denies viewing' do
          expect(policy.viewable_by_free_user?).to be false
        end
      end
      
      context 'and project has no first_published_at' do
        context 'but created_at is older than 14 days' do
          let(:project) { create(:project, created_at: 20.days.ago, first_published_at: nil) }
          
          it 'allows viewing (fallback to created_at)' do
            expect(policy.viewable_by_free_user?).to be true
          end
        end
        
        context 'and created_at is newer than 14 days' do
          let(:project) { create(:project, created_at: 5.days.ago, first_published_at: nil) }
          
          it 'denies viewing' do
            expect(policy.viewable_by_free_user?).to be false
          end
        end
      end
      
      context 'when user is the project owner' do
        let(:user) { project_owner }
        let(:project) { create(:project, user: project_owner, first_published_at: 1.day.ago) }
        
        it 'allows viewing their own project regardless of age' do
          expect(policy.viewable_by_free_user?).to be true
        end
      end
    end
  end

  describe '#can_create_summary_only?' do
    context 'when user has active subscription' do
      let(:user) { subscribed_user }
      
      it 'allows creating summary_only projects' do
        expect(policy.can_create_summary_only?).to be true
      end
    end
    
    context 'when user is free (no subscription)' do
      let(:user) { free_user }
      
      it 'denies creating summary_only projects' do
        expect(policy.can_create_summary_only?).to be false
      end
    end
  end

  describe '#can_request_access?' do
    context 'when user has active subscription' do
      let(:user) { subscribed_user }
      
      it 'allows requesting access to projects' do
        expect(policy.can_request_access?).to be true
      end
    end
    
    context 'when user is free (no subscription)' do
      let(:user) { free_user }
      
      it 'denies requesting access to projects' do
        expect(policy.can_request_access?).to be false
      end
    end
  end

  describe '#show?' do
    context 'when free user tries to view new project' do
      let(:user) { free_user }
      let(:project) { create(:project, user: project_owner, first_published_at: 5.days.ago) }
      
      before do
        # Approve the project as admin
        project.update_column(:approved, true)
        # Give user explicit access
        create(:project_auth, project: project, user: free_user, access_level: 'full_details')
      end
      
      it 'denies access even with ProjectAuth due to 14-day restriction' do
        expect(policy.show?).to be false
      end
    end
    
    context 'when free user tries to view old project' do
      let(:user) { free_user }
      let(:project) { create(:project, user: project_owner, first_published_at: 20.days.ago) }
      
      before do
        # Approve the project as admin
        project.update_column(:approved, true)
        create(:project_auth, project: project, user: free_user, access_level: 'full_details')
      end
      
      it 'allows access with ProjectAuth' do
        expect(policy.show?).to be true
      end
    end
  end

  describe '#view_full_details?' do
    context 'when free user with network connection' do
      let(:user) { free_user }
      let(:project) do
        create(:project, 
          user: project_owner,
          first_published_at: 20.days.ago,
          full_access: true,
          network_only: true
        )
      end
      
      before do
        # Approve the project as admin
        project.update_column(:approved, true)
        create(:network_connection, inviter: project_owner, invitee: free_user)
      end
      
      it 'allows access to old projects with automatic sharing' do
        expect(policy.view_full_details?).to be true
      end
    end
    
    context 'when free user tries to access new project with automatic sharing' do
      let(:user) { free_user }
      let(:project) do
        create(:project, 
          user: project_owner,
          first_published_at: 5.days.ago,
          full_access: true,
          semi_public: true,
          network_only: false
        )
      end
      
      before do
        # Approve the project as admin
        project.update_column(:approved, true)
      end
      
      it 'denies access due to 14-day restriction' do
        expect(policy.view_full_details?).to be false
      end
    end
  end

  describe '#create?' do
    context 'when user is free' do
      let(:user) { free_user }
      
      it 'allows creating projects (gate removed)' do
        expect(policy.create?).to be true
      end
    end
  end

  describe '#upload_files?' do
    context 'when user is free' do
      let(:user) { free_user }
      
      it 'allows uploading files (gate removed)' do
        expect(policy.upload_files?).to be true
      end
    end
  end
end