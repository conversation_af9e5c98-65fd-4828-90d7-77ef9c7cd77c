# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Subscriptions', type: :request do
  # Include Devise test helpers for signing in users in request specs
  include Devise::Test::IntegrationHelpers

  let(:user) { create(:user) }
  let!(:premium_plan) { create(:plan, :premium, stripe_price_id: 'price_premium_123') }

  # --- Test Setup ---
  # This block runs before each test in this file
  before do
    # For a request spec, we must create a complete and valid user profile
    # so that the `ensure_profile_complete` before_action passes.
    create(:user_profile,
           user: user,
           first_name: '<PERSON>',
           last_name: 'Doe',
           city: 'New York',
           country: 'USA',
           profile_completed: true)

    # Sign in the user to establish a session
    sign_in user
  end

  # --- Tests for GET /subscriptions (Pricing Page) ---
  describe 'GET /subscriptions' do
    it 'succeeds and renders the pricing page for a signed-in user' do
      get subscriptions_path
      expect(response).to have_http_status(:ok)
      expect(response.body).to include(premium_plan.name)
    end
  end

  # --- Tests for GET /subscriptions/success (Checkout Success Callback) ---
  describe 'GET /subscriptions/success' do
    let(:session_id) { 'cs_test_123456' }
    let(:stripe_subscription_id) { 'sub_test_abc123' }
    
    context 'when a valid, active subscription exists' do
      it 'redirects to the subscriptions page with a success notice' do
        # Create an active subscription for the user
        subscription = create(:subscription, user: user, plan: premium_plan, 
                            status: 'active', stripe_subscription_id: stripe_subscription_id)
        
        # Mock Stripe session retrieval
        mock_session = double('Stripe::Checkout::Session', subscription: stripe_subscription_id)
        allow(Stripe::Checkout::Session).to receive(:retrieve).with(session_id).and_return(mock_session)

        get success_subscriptions_path(session_id: session_id)
        
        expect(response).to redirect_to(subscriptions_path)
        follow_redirect! # Optional: allows you to inspect the page after the redirect

        expect(flash[:notice]).to eq(I18n.t('subscriptions.checkout_success_generic'))
      end
    end

    context 'when no subscription is found for the user' do
      it 'redirects to the subscriptions page with an error alert' do
        # Mock Stripe session retrieval
        mock_session = double('Stripe::Checkout::Session', subscription: 'sub_nonexistent')
        allow(Stripe::Checkout::Session).to receive(:retrieve).with(session_id).and_return(mock_session)
        
        get success_subscriptions_path(session_id: session_id)

        expect(response).to redirect_to(subscriptions_path)
        expect(flash[:alert]).to eq(I18n.t('subscriptions.checkout_error_no_subscription'))
      end
    end

    context 'when the subscription is incomplete' do
      it 'redirects to the subscriptions page with an incomplete alert' do
        # Create an incomplete subscription for the user
        subscription = create(:subscription, user: user, plan: premium_plan, 
                            status: 'incomplete', stripe_subscription_id: stripe_subscription_id)
        
        # Mock Stripe session retrieval
        mock_session = double('Stripe::Checkout::Session', subscription: stripe_subscription_id)
        allow(Stripe::Checkout::Session).to receive(:retrieve).with(session_id).and_return(mock_session)

        get success_subscriptions_path(session_id: session_id)

        expect(response).to redirect_to(subscriptions_path)
        expect(flash[:alert]).to eq(I18n.t('subscriptions.checkout_incomplete'))
      end
    end

    context 'when the subscription has an unexpected status' do
      it 'redirects with an alert showing the unexpected status' do
        # Create a subscription with an unusual status like 'canceled'
        subscription = create(:subscription, user: user, plan: premium_plan, 
                            status: 'canceled', stripe_subscription_id: stripe_subscription_id)
        
        # Mock Stripe session retrieval
        mock_session = double('Stripe::Checkout::Session', subscription: stripe_subscription_id)
        allow(Stripe::Checkout::Session).to receive(:retrieve).with(session_id).and_return(mock_session)

        get success_subscriptions_path(session_id: session_id)

        expect(response).to redirect_to(subscriptions_path)
        expect(flash[:alert]).to eq(I18n.t('subscriptions.checkout_error_status', status: 'Canceled'))
      end
    end
    
    context 'when Stripe session retrieval fails' do
      it 'redirects with an error when session_id is invalid' do
        # Mock Stripe error
        allow(Stripe::Checkout::Session).to receive(:retrieve)
          .with('invalid_session')
          .and_raise(Stripe::InvalidRequestError.new('No such checkout session', 'session_id'))
        
        get success_subscriptions_path(session_id: 'invalid_session')

        expect(response).to redirect_to(subscriptions_path)
        expect(flash[:alert]).to eq(I18n.t('subscriptions.checkout_error_no_subscription'))
      end
    end
  end
end
