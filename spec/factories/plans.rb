# ABOUTME: Factory for creating Plan test records
# ABOUTME: Defines default attributes for subscription plans in tests
FactoryBot.define do
  factory :plan do
    sequence(:name) { |n| "Plan #{n}" }
    sequence(:stripe_price_id) { |n| "price_test_#{n}" }
    sequence(:stripe_product_id) { |n| "prod_test_#{n}" }
    price_cents { 1999 } # $19.99 in cents
    interval { 'month' }
    tier { 'premium' }
    metadata { { 'projects' => 10, 'wants' => 10, 'network_connections' => 100 } }
    active { true }
    
    trait :free do
      name { 'Free Plan' }
      price_cents { 0 }
      tier { 'free' }
      metadata { { 'projects' => 3, 'wants' => 3, 'network_connections' => 5 } }
    end
    
    trait :premium do
      name { 'Premium Plan' }
      price_cents { 1999 }
      tier { 'premium' }
      metadata { { 'projects' => 50, 'wants' => 50, 'network_connections' => 500 } }
    end
    
    trait :pilot do
      name { 'Pilot Plan' }
      price_cents { 9999 }
      tier { 'pilot' }
      metadata { { 'projects' => -1, 'wants' => -1, 'network_connections' => -1 } } # -1 means unlimited
    end
    
    trait :yearly do
      interval { 'year' }
      price_cents { 19999 } # Annual discount
    end
  end
end