# ABOUTME: RSpec tests for Subscription model validating subscription lifecycle and Stripe integration
# ABOUTME: Tests cover associations, validations, status management, and payment provider integration
require 'rails_helper'

RSpec.describe Subscription, type: :model do
  let(:user) { User.create!(email: '<EMAIL>', password: 'password123') }
  let(:plan) { Plan.create!(name: 'Premium Monthly', tier: 'premium', interval: 'month', price_cents: 999) }
  
  describe 'associations' do
    it 'belongs to user' do
      expect(Subscription.reflect_on_association(:user)).to be_present
      expect(Subscription.reflect_on_association(:user).macro).to eq(:belongs_to)
    end
    
    it 'belongs to plan' do
      expect(Subscription.reflect_on_association(:plan)).to be_present
      expect(Subscription.reflect_on_association(:plan).macro).to eq(:belongs_to)
    end
  end

  describe 'validations' do
    let(:valid_subscription) { Subscription.new(user: user, plan: plan, status: 'active', payment_provider: 'manual') }
    
    it 'validates presence of user' do
      subscription = Subscription.new(plan: plan, status: 'active', payment_provider: 'manual')
      expect(subscription).not_to be_valid
      expect(subscription.errors[:user]).not_to be_empty
    end
    
    it 'validates presence of plan' do
      subscription = Subscription.new(user: user, status: 'active', payment_provider: 'manual')
      expect(subscription).not_to be_valid
      expect(subscription.errors[:plan]).not_to be_empty
    end
    
    it 'validates presence of status' do
      subscription = Subscription.new(user: user, plan: plan, payment_provider: 'manual')
      subscription.status = nil
      expect(subscription).not_to be_valid
      expect(subscription.errors[:status]).not_to be_empty
    end
    
    it 'validates presence of payment_provider' do
      subscription = Subscription.new(user: user, plan: plan, status: 'active')
      subscription.payment_provider = nil
      expect(subscription).not_to be_valid
      expect(subscription.errors[:payment_provider]).not_to be_empty
    end
    
    it 'allows valid subscription' do
      expect(valid_subscription).to be_valid
    end
  end

  describe 'enums' do
    it 'defines status enum' do
      expect(Subscription.statuses).to eq({
        'active' => 0,
        'trialing' => 1,
        'past_due' => 2,
        'canceled' => 3,
        'unpaid' => 4,
        'incomplete' => 5,
        'incomplete_expired' => 6
      })
    end

    it 'defines payment_provider enum' do
      expect(Subscription.payment_providers).to eq({
        'stripe' => 0,
        'paddle' => 1,
        'lemonsqueezy' => 2,
        'manual' => 3,
        'referral' => 4
      })
    end
  end

  describe 'scopes' do
    let!(:active_sub) { Subscription.create!(user: user, plan: plan, status: 'active', payment_provider: 'manual') }
    let!(:trialing_sub) { Subscription.create!(user: user, plan: plan, status: 'trialing', payment_provider: 'manual') }
    let!(:canceled_sub) { Subscription.create!(user: user, plan: plan, status: 'canceled', payment_provider: 'manual') }
    let!(:past_due_sub) { Subscription.create!(user: user, plan: plan, status: 'past_due', payment_provider: 'manual') }

    describe '.active_or_trialing' do
      it 'returns active and trialing subscriptions' do
        expect(Subscription.active_or_trialing).to include(active_sub, trialing_sub)
        expect(Subscription.active_or_trialing).not_to include(canceled_sub, past_due_sub)
      end
    end

    describe '.expired' do
      it 'returns expired subscriptions' do
        expect(Subscription.expired).to include(canceled_sub)
        expect(Subscription.expired).not_to include(active_sub, trialing_sub)
      end
    end

    describe '.needs_attention' do
      it 'returns subscriptions needing payment attention' do
        expect(Subscription.needs_attention).to include(past_due_sub)
        expect(Subscription.needs_attention).not_to include(active_sub, canceled_sub)
      end
    end
  end

  describe 'callbacks' do
    describe '#set_default_periods' do
      context 'for monthly plan' do
        it 'sets period end to 1 month from now' do
          subscription = Subscription.create!(user: user, plan: plan, status: 'active', payment_provider: 'manual')
          expect(subscription.current_period_start).to be_within(1.second).of(Time.current)
          expect(subscription.current_period_end).to be_within(1.second).of(1.month.from_now)
        end
      end

      context 'for yearly plan' do
        let(:yearly_plan) { Plan.create!(name: 'Premium Yearly', tier: 'premium', interval: 'year', price_cents: 9999) }
        
        it 'sets period end to 1 year from now' do
          subscription = Subscription.create!(user: user, plan: yearly_plan, status: 'active', payment_provider: 'manual')
          expect(subscription.current_period_end).to be_within(1.second).of(1.year.from_now)
        end
      end

      context 'for one-time plan' do
        let(:lifetime_plan) { Plan.create!(name: 'Premium Lifetime', tier: 'premium', interval: 'one_time', price_cents: 29999) }
        
        it 'sets period end to 100 years from now' do
          subscription = Subscription.create!(user: user, plan: lifetime_plan, status: 'active', payment_provider: 'manual')
          expect(subscription.current_period_end).to be_within(1.second).of(100.years.from_now)
        end
      end
    end
  end

  describe 'instance methods' do
    let(:subscription) { Subscription.create!(user: user, plan: plan, status: 'active', payment_provider: 'stripe') }

    describe '#expired?' do
      it 'returns true for expired statuses' do
        subscription.update!(status: 'canceled')
        expect(subscription.expired?).to be true
        
        subscription.update!(status: 'unpaid')
        expect(subscription.expired?).to be true
        
        subscription.update!(status: 'incomplete_expired')
        expect(subscription.expired?).to be true
      end

      it 'returns false for active statuses' do
        subscription.update!(status: 'active')
        expect(subscription.expired?).to be false
        
        subscription.update!(status: 'trialing')
        expect(subscription.expired?).to be false
      end
    end

    describe '#needs_payment?' do
      it 'returns true for payment-needed statuses' do
        subscription.update!(status: 'past_due')
        expect(subscription.needs_payment?).to be true
        
        subscription.update!(status: 'unpaid')
        expect(subscription.needs_payment?).to be true
        
        subscription.update!(status: 'incomplete')
        expect(subscription.needs_payment?).to be true
      end

      it 'returns false for other statuses' do
        subscription.update!(status: 'active')
        expect(subscription.needs_payment?).to be false
      end
    end

    describe '#can_access_features?' do
      it 'returns true for active and trialing' do
        subscription.update!(status: 'active')
        expect(subscription.can_access_features?).to be true
        
        subscription.update!(status: 'trialing')
        expect(subscription.can_access_features?).to be true
      end

      it 'returns false for other statuses' do
        subscription.update!(status: 'canceled')
        expect(subscription.can_access_features?).to be false
      end
    end

    describe '#in_trial?' do
      it 'returns true when trialing with future trial_end' do
        subscription.update!(status: 'trialing', trial_end: 7.days.from_now)
        expect(subscription.in_trial?).to be true
      end

      it 'returns false when not trialing' do
        subscription.update!(status: 'active')
        expect(subscription.in_trial?).to be false
      end

      it 'returns false when trial has ended' do
        subscription.update!(status: 'trialing', trial_end: 1.day.ago)
        expect(subscription.in_trial?).to be false
      end
    end

    describe '#trial_days_remaining' do
      it 'returns days remaining in trial' do
        subscription.update!(status: 'trialing', trial_end: 7.days.from_now)
        expect(subscription.trial_days_remaining).to eq(7)
      end

      it 'returns 0 when not in trial' do
        subscription.update!(status: 'active')
        expect(subscription.trial_days_remaining).to eq(0)
      end
    end

    describe '#days_until_renewal' do
      it 'returns days until renewal' do
        subscription.update!(current_period_end: 30.days.from_now)
        expect(subscription.days_until_renewal).to eq(30)
      end

      it 'returns 0 for past period end' do
        subscription.update!(current_period_end: 1.day.ago)
        expect(subscription.days_until_renewal).to eq(0)
      end

      it 'returns nil when no period end' do
        subscription.update!(current_period_end: nil)
        expect(subscription.days_until_renewal).to be_nil
      end
    end

    describe '#cancel!' do
      it 'cancels subscription at period end by default' do
        subscription.cancel!(reason: 'Too expensive')
        
        expect(subscription.cancel_reason).to eq('Too expensive')
        expect(subscription.cancel_at_period_end).to be true
        expect(subscription.canceled_at).to be_within(1.second).of(Time.current)
        expect(subscription.status).to eq('active') # Still active until period end
        expect(subscription.ended_at).to be_nil
      end

      it 'cancels subscription immediately when specified' do
        subscription.cancel!(reason: 'Not needed', at_period_end: false)
        
        expect(subscription.cancel_reason).to eq('Not needed')
        expect(subscription.cancel_at_period_end).to be false
        expect(subscription.canceled_at).to be_within(1.second).of(Time.current)
        expect(subscription.status).to eq('canceled')
        expect(subscription.ended_at).to be_within(1.second).of(Time.current)
      end
    end

    describe '#reactivate!' do
      context 'when subscription can be reactivated' do
        before do
          subscription.update!(
            status: 'canceled',
            current_period_end: 7.days.from_now,
            cancel_reason: 'Test',
            cancel_at_period_end: true,
            canceled_at: Time.current
          )
        end

        it 'reactivates the subscription' do
          expect(subscription.reactivate!).to be true
          expect(subscription.status).to eq('active')
          expect(subscription.cancel_reason).to be_nil
          expect(subscription.cancel_at_period_end).to be false
          expect(subscription.canceled_at).to be_nil
          expect(subscription.ended_at).to be_nil
        end
      end

      context 'when subscription cannot be reactivated' do
        before do
          subscription.update!(
            status: 'canceled',
            current_period_end: 1.day.ago
          )
        end

        it 'returns false' do
          expect(subscription.reactivate!).to be false
        end
      end
    end

    describe '#can_reactivate?' do
      it 'returns true for canceled subscription with future period end' do
        subscription.update!(status: 'canceled', current_period_end: 7.days.from_now)
        expect(subscription.can_reactivate?).to be true
      end

      it 'returns false for active subscription' do
        subscription.update!(status: 'active')
        expect(subscription.can_reactivate?).to be false
      end

      it 'returns false for canceled subscription with past period end' do
        subscription.update!(status: 'canceled', current_period_end: 1.day.ago)
        expect(subscription.can_reactivate?).to be false
      end
    end

    describe '#display_status' do
      it 'shows canceling status for active with cancel_at_period_end' do
        subscription.update!(
          status: 'active',
          cancel_at_period_end: true,
          current_period_end: Date.new(2025, 12, 31)
        )
        expect(subscription.display_status).to eq('Active (Canceling on December 31, 2025)')
      end

      it 'shows normal status otherwise' do
        subscription.update!(status: 'active')
        expect(subscription.display_status).to eq('Active')
      end
    end

    describe '#provider_name' do
      it 'returns humanized provider name' do
        subscription.update!(payment_provider: 'stripe')
        expect(subscription.provider_name).to eq('Stripe')
        
        subscription.update!(payment_provider: 'lemonsqueezy')
        expect(subscription.provider_name).to eq('Lemonsqueezy')
      end
    end
  end

  describe '.map_stripe_status' do
    it 'maps stripe statuses correctly' do
      expect(Subscription.map_stripe_status('active')).to eq(:active)
      expect(Subscription.map_stripe_status('trialing')).to eq(:trialing)
      expect(Subscription.map_stripe_status('past_due')).to eq(:past_due)
      expect(Subscription.map_stripe_status('canceled')).to eq(:canceled)
      expect(Subscription.map_stripe_status('unpaid')).to eq(:unpaid)
      expect(Subscription.map_stripe_status('incomplete')).to eq(:incomplete)
      expect(Subscription.map_stripe_status('incomplete_expired')).to eq(:incomplete_expired)
      expect(Subscription.map_stripe_status('unknown')).to eq(:active)
    end
  end
end