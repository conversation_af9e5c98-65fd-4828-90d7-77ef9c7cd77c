{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(bundle exec rspec:*)", "mcp__gmail__gmail_send_email", "mcp__gemini__thinkdeep", "Bash(bin/rails console:*)", "Bash(ls:*)", "Bash(rails console:*)", "mcp__gemini__codereview", "Bash(gh api:*)", "Bash(gh pr view:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["linear"]}