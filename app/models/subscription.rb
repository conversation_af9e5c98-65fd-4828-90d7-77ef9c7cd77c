# ABOUTME: Subscription model tracking user subscription history with full Stripe compatibility
# ABOUTME: Maintains complete history for analytics and supports multiple payment providers
class Subscription < ApplicationRecord
  # Associations
  belongs_to :user
  belongs_to :plan

  # Stripe-matching statuses
  enum status: {
    active: 0,
    trialing: 1,
    past_due: 2,
    canceled: 3,
    unpaid: 4,
    incomplete: 5,
    incomplete_expired: 6
  }

  enum payment_provider: {
    stripe: 0,
    paddle: 1,
    lemonsqueezy: 2,
    manual: 3,
    referral: 4
  }

  # Validations
  validates :user, presence: true
  validates :plan, presence: true
  validates :status, presence: true
  validates :payment_provider, presence: true
  validates :stripe_subscription_id, uniqueness: { allow_nil: true }
  validates :paddle_subscription_id, uniqueness: { allow_nil: true }
  validates :lemonsqueezy_subscription_id, uniqueness: { allow_nil: true }

  # Scopes for common queries
  scope :active_or_trialing, -> { where(status: [:active, :trialing]) }
  scope :expired, -> { where(status: [:canceled, :unpaid, :incomplete_expired]) }
  scope :needs_attention, -> { where(status: [:past_due, :unpaid]) }
  scope :active_only, -> { where(status: :active) }
  scope :trialing_only, -> { where(status: :trialing) }
  scope :recent, -> { order(created_at: :desc) }

  # Callbacks
  before_validation :set_default_periods, on: :create

  # Sync with Stripe
  def sync_with_stripe!(stripe_subscription)
    update!(
      stripe_status: stripe_subscription.status,
      status: map_stripe_status(stripe_subscription.status),
      current_period_start: Time.at(stripe_subscription.current_period_start),
      current_period_end: Time.at(stripe_subscription.current_period_end),
      canceled_at: stripe_subscription.canceled_at ? Time.at(stripe_subscription.canceled_at) : nil,
      cancel_at_period_end: stripe_subscription.cancel_at_period_end,
      provider_data: stripe_subscription.to_h
    )
  end

  # Create from Stripe subscription
  def self.create_from_stripe!(user, stripe_subscription, plan)
    create!(
      user: user,
      plan: plan,
      stripe_subscription_id: stripe_subscription.id,
      stripe_customer_id: stripe_subscription.customer,
      stripe_status: stripe_subscription.status,
      status: map_stripe_status(stripe_subscription.status),
      current_period_start: Time.at(stripe_subscription.current_period_start),
      current_period_end: Time.at(stripe_subscription.current_period_end),
      trial_start: stripe_subscription.trial_start ? Time.at(stripe_subscription.trial_start) : nil,
      trial_end: stripe_subscription.trial_end ? Time.at(stripe_subscription.trial_end) : nil,
      payment_provider: :stripe,
      provider_data: stripe_subscription.to_h
    )
  end

  # Status helpers
  def expired?
    [:canceled, :unpaid, :incomplete_expired].include?(status.to_sym)
  end

  def needs_payment?
    [:past_due, :unpaid, :incomplete].include?(status.to_sym)
  end

  def can_access_features?
    [:active, :trialing].include?(status.to_sym)
  end

  def in_trial?
    status == 'trialing' && trial_end&.future?
  end

  def trial_days_remaining
    return 0 unless in_trial?
    ((trial_end - Time.current) / 1.day).ceil
  end

  def days_until_renewal
    return nil unless current_period_end
    return 0 if current_period_end.past?
    ((current_period_end - Time.current) / 1.day).ceil
  end

  # Cancel subscription
  def cancel!(reason: nil, at_period_end: true)
    update!(
      cancel_reason: reason,
      cancel_at_period_end: at_period_end,
      canceled_at: Time.current,
      status: at_period_end ? status : :canceled,
      ended_at: at_period_end ? nil : Time.current
    )
  end

  # Reactivate subscription
  def reactivate!
    return false unless can_reactivate?
    
    update!(
      status: :active,
      cancel_reason: nil,
      cancel_at_period_end: false,
      canceled_at: nil,
      ended_at: nil
    )
  end

  def can_reactivate?
    canceled? && current_period_end&.future?
  end

  # Display helpers
  def display_status
    if cancel_at_period_end? && active?
      "Active (Canceling on #{current_period_end.strftime('%B %d, %Y')})"
    else
      status.humanize
    end
  end

  def provider_name
    payment_provider.humanize
  end

  private

  def set_default_periods
    return if current_period_start.present?

    self.current_period_start = Time.current
    
    case plan&.interval
    when 'month'
      self.current_period_end = 1.month.from_now
    when 'year'
      self.current_period_end = 1.year.from_now
    when 'one_time'
      self.current_period_end = 100.years.from_now # Effectively never expires
    end
  end

  def self.map_stripe_status(stripe_status)
    # Map Stripe status strings to our enum values
    case stripe_status.to_s
    when 'active' then :active
    when 'trialing' then :trialing
    when 'past_due' then :past_due
    when 'canceled' then :canceled
    when 'unpaid' then :unpaid
    when 'incomplete' then :incomplete
    when 'incomplete_expired' then :incomplete_expired
    else :active
    end
  end

  def map_stripe_status(stripe_status)
    self.class.map_stripe_status(stripe_status)
  end
end