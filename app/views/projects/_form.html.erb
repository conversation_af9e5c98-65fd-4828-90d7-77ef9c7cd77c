<div class="flex g1">
  <div class="main-content">
      
    <%= form_with(model: project, id: "project_form", html: { class: 'large-form', data: { project_id: project.persisted? ? project.id : nil } }) do |form| %>

      <% if project.errors.any? %>
        <div id="error_explanation">
          <ul>
            <% project.errors.each do |error| %>
              <li><%= error.message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      
      <div class="mb-1">
        <div class="summary-field">
          <%= form.label :summary, t('models.project.attributes.summary', default: 'Title') + ' *' %>
          <%= form.text_field :summary,
                            value: project.summary,
                            placeholder:t('projects.form.title_placeholder'),
                            class:'title-input',
                            maxlength: 123,
                            oninput: "updateCharacterCount(this)" %>
          <div class="character-count ml-1">
            <span id="summary-char-count"><%= project.summary&.length %></span>/123 <%= t('projects.form.characters') %>
          </div>
          
        </div>
        <script>
          function updateCharacterCount(input) {
            const maxLength = input.maxLength;
            const currentLength = input.value.length;
            document.getElementById('summary-char-count').textContent = currentLength;
            
            // Show UX notice for summary manual save requirement
            checkSummaryChangeNotice();
          }
          
          // UX: Show notice when summary changes on published projects
          function checkSummaryChangeNotice() {
            const summaryInput = document.querySelector('input[name="project[summary]"]');
            const projectStatusToggle = document.getElementById('project_project_status');
            const notice = document.getElementById('summary-save-notice');
            
            if (!summaryInput || !projectStatusToggle || !notice) {
              console.log('Missing elements:', { summaryInput, projectStatusToggle, notice });
              return;
            }
            
            const isPublished = projectStatusToggle.checked;
            const originalSummary = '<%= j(@project.summary) %>';
            const currentSummary = summaryInput.value;
            const summaryChanged = currentSummary !== originalSummary;
            
            console.log('Summary check:', { isPublished, originalSummary, currentSummary, summaryChanged });
            
            // Show notice when summary changed (draft or published)
            if (summaryChanged) {
              notice.style.display = 'block';
            } else {
              notice.style.display = 'none';
            }
          }
          
          // Initialize and bind events
          document.addEventListener('DOMContentLoaded', function() {
            const summaryInput = document.querySelector('input[name="project[summary]"]');
            const projectStatusToggle = document.getElementById('project_project_status');
            
            if (summaryInput) {
              summaryInput.addEventListener('input', checkSummaryChangeNotice);
            }
            
            if (projectStatusToggle) {
              projectStatusToggle.addEventListener('change', checkSummaryChangeNotice);
            }
            
            // Initial check
            checkSummaryChangeNotice();
          });
        </script>
        
      </div>

      <div class="mb-1">
        <%= form.text_area :full_description, rows: 40, placeholder: t('projects.form.description_placeholder'),style: "max-height: 50vh; resize: vertical;" %>
      </div>

     
      <div class="mb-1">
        <%= form.label :location, t('models.attributes.common.location') + ' *' %></h3>
        <%= form.text_field :location,
                            id: 'project_location',
                            autocomplete: 'off',
                            placeholder: t('projects.form.location_placeholder') %>
        <%= form.hidden_field :latitude, id: 'project_latitude' %>
        <%= form.hidden_field :longitude, id: 'project_longitude' %>
        <%= form.hidden_field :country, id: 'project_country' %>
        <%= form.hidden_field :country_code, id: 'project_country_code' %>
      </div>
      
      <div class="flex mb-1 flex-row g1"> 
        <div class="flex-inline flex-column mb-1">
          <%= form.label :project_type, t('models.project.attributes.project_type') + ' *' %>
          <%= form.select :project_type, 
              Project.translated_project_types, 
              { prompt: t("projects.form.project_type_placeholder", default: "Select Project Type") },
              { onchange: "updateCategories(this.value)" } %>
        </div>

        <div class="flex-inline flex-column mb-1">
          <%= form.label :category, t('models.project.attributes.category') + ' *' %>
          <%= form.select :category, 
              @project.project_type ? Project.translated_categories_for(@project.project_type) : [],
              { prompt: t('projects.form.category_placeholder') },
              { onchange: "updateSubcategories(this.value)" } %>
        </div>

        <div class="flex-inline flex-column mb-1">
          <%= form.label :subcategory, t('models.project.attributes.subcategory') + ' *' %>
          <%= form.select :subcategory,
              @project.category ? Project::CATEGORIES[@project.category.to_sym].map { |s| [s.humanize, s] } : [],
              { prompt: t('projects.form.subcategory_placeholder') } %>
        </div>
      </div>

      <div class="mb-1 row g1" id="real-estate-fields" style="display: none;">
        <div class="flex-inline flex-column mb-1">
          <%= form.label :land_area, t('models.project.attributes.land_area') %></h3>
          <%= form.text_field :land_area %>
        </div>
        <div class="flex-inline flex-column mb-1">
          <%= form.label :area_unit, t('models.project.attributes.area_unit') %></h3>
          <%= form.text_field :area_unit %>
        </div>
      </div>
      
      <div class="mb-1 row g1">
        <div class="flex-inline flex-column mb-1">
          <%= form.label t('models.project.attributes.price_value') %></h3>
          <%= form.text_field :price_value, inputmode: 'decimal' %>
        </div>
        <div class="flex-inline flex-column mb-1">
          <%= form.label t('models.project.attributes.price_currency') %></h3>
          <%= form.select :price_currency, options_for_select([['', ''], ['EUR', 'EUR']], selected: project.price_currency) %>
        </div>
        <div class="flex-inline flex-column mb-1">
          <%= form.label t('models.project.attributes.price_text') %></h3>
          <%= form.text_field :price_text %>
        </div>
      </div>

      <div class="mb-1 row g1">
        <div class="flex-inline flex-column mb-1">
          <%= form.label t('models.project.attributes.commission') %></h3>
          <%= form.text_field :commission, inputmode: 'decimal' %>
        </div>
        <div class="flex-inline flex-column mb-1">
          <%= form.label t('models.project.attributes.commission_type') %></h3>
          <%= form.select :commission_type, options_for_select([['', ''], ['EUR', 'EUR'], ['%', '%']], selected: project.commission_type) %>
        </div>
      </div>

      <div class="form-card">
        <div>
          <%= form.label t('projects.form.add_files') %></h3>
          
          <!-- Upload translations for JavaScript -->
          <div id="js-translations" style="display: none;"
               data-file-uploads="<%= j t('common.uploads.messages.file_uploads') %>"
               data-loading-file="<%= j t('common.uploads.messages.loading_file') %>"
               data-select-files-first="<%= j t('common.uploads.messages.select_files_first') %>"
               data-upload-failed="<%= j t('common.uploads.messages.upload_failed') %>"
               data-upload-successful="<%= j t('common.uploads.messages.upload_successful') %>"
               data-too-many-files="<%= j t('common.uploads.messages.too_many_files') %>"
               data-file-too-large="<%= j t('common.uploads.messages.file_too_large') %>"
               data-unsupported-file-type="<%= j t('common.uploads.messages.unsupported_file_type') %>"
               data-rate-limit-exceeded="<%= j t('common.uploads.messages.rate_limit_exceeded') %>"
               data-status-preparing="<%= j t('common.uploads.statuses.preparing') %>"
               data-status-uploading="<%= j t('common.uploads.statuses.uploading') %>"
               data-status-processing="<%= j t('common.uploads.statuses.processing') %>"
               data-status-transferred="<%= j t('common.uploads.statuses.transferred') %>"
               data-status-completed="<%= j t('common.uploads.statuses.completed') %>"
               data-status-failed="<%= j t('common.uploads.statuses.failed') %>"
               data-status-cancelled="<%= j t('common.uploads.statuses.cancelled') %>"
               data-action-cancel="<%= j t('common.uploads.actions.cancel') %>"></div>

          <!-- Simple upload notification -->
          <% if @active_uploads_count&.positive? %>
            <div class="upload-notification info-notification">
              <%= t('common.words.upload', count: @active_uploads_count) %> <%= t('projects.form.active_uploads', count: @active_uploads_count, default: 'being processed in the background') %>
              
              <% Upload.where(target: @project, status: ['pending', 'transferred', 'processing']).each do |upload| %>
                <div class="upload-item">
                  <strong><%= upload.original_filename %></strong> - 
                  <%= t('projects.form.started_time_ago', time: time_ago_in_words(upload.created_at), default: "Started %{time} ago") %>
                </div>
              <% end %>
              
              <% if @stuck_uploads&.any? %>
                <div class="stuck-upload-actions">
                  <%= t('common.words.upload', count: @stuck_uploads.count) %> <%= t('projects.form.stuck_uploads', count: @stuck_uploads.count, default: 'appears stuck (no progress for 30+ minutes)') %>
                  <button type="button" 
                          class="cleanup-stuck-btn"
                          onclick="cleanupStuckUploads()"
                          data-cleaning-text="<%= t('projects.form.cleaning_up', default: 'Cleaning up...') %>"
                          data-cleanup-failed-text="<%= t('projects.form.cleanup_failed_alert', default: 'Failed to cleanup stuck uploads. Please try again.') %>">
                    <%= t('projects.form.cleanup_stuck_uploads', default: 'Cleanup Stuck Uploads') %>
                  </button>
                </div>
              <% end %>
            </div>
          <% end %>

          <!-- Modern Upload Interface -->
          <div class="upload-drop-zone" id="upload-drop-zone" style="position: relative;">
            <div class="upload-drop-text">
              <div class="primary-text"><%= t('projects.form.drop_zone.primary_text', default: 'Drop files here or click to browse') %></div>
              <div class="secondary-text">
                <%= t('projects.form.drop_zone.supported_files', default: 'Supported: PDF, Images') %>
              </div>
              <div class="secondary-text">
                <%= t('projects.form.drop_zone.max_size', default: 'Maximum size: 10MB per file') %>
                <%= t('projects.form.drop_zone.max_upload', default: 'up to 5 files at once') %>
              </div>
              <div class="secondary-text">
                <%= t('projects.form.drop_zone.upload_note') %>
              </div>
            </div>
            
            <%= file_field_tag 'upload_files[]', 
                multiple: true,
                accept: "application/pdf,image/png,image/jpeg,image/tiff,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/plain,text/csv",
                data: {
                  upload_handler: true,
                  target_type: 'Project',
                  target_id: @project.persisted? ? @project.id : nil
                },
                style: "position: absolute; top: 0; left: 0; opacity: 0; width: 100%; height: 100%; cursor: pointer;" %>
            
            <% unless @project.persisted? %>
              <div class="upload-notice">
                <small style="color: #dc2626;"><%= t('projects.form.upload_notice', default: 'Note: Files can be uploaded once the project is created') %></small>
              </div>
            <% end %>
          </div>
          
          <span class="file-error" id="file-error"></span>
          
          <!-- Keep existing files as hidden fields for form compatibility -->
          <% @project.private_files.each do |file| %>
            <%= form.hidden_field :private_files, multiple: true, value: file.signed_id %>
          <% end %>
        </div>
        <br/>
      </div>
      <div class="form-card">
        <div class="file-grid">
          <% if @project.persisted? && @project.private_files.attached? %>
            <% @project.private_files.each do |file| %>
              <div class="file-item" 
                   data-file-id="<%= file.id %>"
                   data-content-type="<%= file.content_type %>"
                   data-project-id="<%= @project.id %>">
                
                <div class="file-thumbnail-container">
                  <% if supports_thumbnail?(file) %>
                    <% thumbnail_url = safe_thumbnail_url(@project, file) %>
                    <% if thumbnail_url %>
                      <img src="<%= thumbnail_url %>" 
                           alt="<%= file.filename %>"
                           class="file-thumbnail" />
                    <% else %>
                      <!-- Fallback if thumbnail generation fails -->
                      <div class="file-icon file-thumbnail-placeholder file-thumbnail <%= file_icon_class(file) %>">
                        <% if file.image? %>
                          <%= heroicon "photo", variant: :outline, options: { class: "icon-48" } %>
                        <% elsif file.content_type == 'application/pdf' %>
                          <%= heroicon "document-text", variant: :outline, options: { class: "icon-48" } %>
                        <% else %>
                          <%= heroicon "document", variant: :outline, options: { class: "icon-48" } %>
                        <% end %>
                      </div>
                    <% end %>
                  <% else %>
                    <!-- Non-thumbnailable files show icon -->
                    <div class="file-icon <%= file_icon_class(file) %>">
                      <%= heroicon "document", variant: :outline, options: { class: "icon-48" } %>
                    </div>
                  <% end %>
                </div>
                
                <div class="file-name">
                  <%= truncate(file.filename.to_s, length: 25) %>
                </div>

                <div class="file-meta">
                  <span class="file-size"><%= number_to_human_size(file.byte_size, precision: 2) %></span>
                  <span class="file-date"><%= file.created_at.strftime("%b %d") %></span>
                </div>
                
                <div class="file-actions">
                  <% download_url = safe_download_url(@project, file) %>
                  <% if download_url %>
                    <a href="<%= download_url %>" class="download-link">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" />
                    </svg>
                      <%= t('projects.form.download', default: 'Stiahnuť') %>
                    </a>
                  <% end %>
                </div>
              </div>
            <% end %>
          <% else %>
            <p><%= t('projects.form.no_files', default: 'No files attached.') %></p>
          <% end %>
        </div>
        <div class="actions">
          <button type="button" onclick="document.getElementById('fileModal').style.display='block'" class="text-link">
            <%= t('projects.form.manage_files') %>
          </button>
        </div>
      </div>


      <script>
        // Cached translations for cleaner JavaScript
        const projectTypes = <%= raw @cached_project_types.to_json %>;
        const categories = <%= raw @cached_categories.to_json %>;
        const translations = {
          projectTypes: <%= raw @cached_translations[:projectTypes].to_json %>,
          categories: <%= raw @cached_translations[:categories].to_json %>,
          subcategories: <%= raw @cached_translations[:subcategories].to_json %>,
          placeholders: {
            category: '<%= j @cached_placeholders[:category] %>',
            subcategory: '<%= j @cached_placeholders[:subcategory] %>'
          }
        };

        // Legacy validation function - now handled by UploadHandler
        function validateFiles(input, maxSizeMB) {
          // File validation is now handled by the UploadHandler JavaScript module
          // This function is kept for backward compatibility but does nothing
          console.log('File validation handled by UploadHandler');
        }
        
        function updateCategories(projectType) {
          const categorySelect = document.getElementById('project_category');
          
          // CRITICAL FIX: Temporarily disable autosave during dropdown manipulation
          // to prevent unwanted autosave triggers during form initialization
          const form = document.getElementById('project_form');
          const originalDataAttribute = form.dataset.skipAutosave;
          form.dataset.skipAutosave = 'true';
          
          categorySelect.innerHTML = '<option value="">' + translations.placeholders.category + '</option>';
          
          if (projectType && projectTypes[projectType]) {
            projectTypes[projectType].forEach(function(category) {
              const option = document.createElement('option');
              option.value = category;
              option.textContent = translations.categories[category] || category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
              categorySelect.appendChild(option);
            });
          }
          
          updateSubcategories('');
          
          // Re-enable autosave after dropdown manipulation is complete
          setTimeout(() => {
            if (originalDataAttribute) {
              form.dataset.skipAutosave = originalDataAttribute;
            } else {
              delete form.dataset.skipAutosave;
            }
          }, 100);
        }

        function updateSubcategories(category) {
          const subcategorySelect = document.getElementById('project_subcategory');
          const currentSubcategory = '<%= @project.subcategory %>'; 

          // CRITICAL FIX: Temporarily disable autosave during dropdown manipulation
          const form = document.getElementById('project_form');
          const originalDataAttribute = form.dataset.skipAutosave;
          form.dataset.skipAutosave = 'true';

          subcategorySelect.innerHTML = '<option value="">' + translations.placeholders.subcategory + '</option>';

          if (category && categories[category]) {
            categories[category].forEach(function(subcategory) {
              const option = document.createElement('option');
              option.value = subcategory;
              option.textContent = translations.subcategories[subcategory] || subcategory.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
              
              if (subcategory === currentSubcategory) {
                option.selected = true;
              }

              subcategorySelect.appendChild(option);
            });
          }
          
          // Re-enable autosave after dropdown manipulation is complete
          setTimeout(() => {
            if (originalDataAttribute) {
              form.dataset.skipAutosave = originalDataAttribute;
            } else {
              delete form.dataset.skipAutosave;
            }
          }, 100);
        }

        // Project_type, category and subcategory dropdowns
        document.addEventListener('DOMContentLoaded', function() {
          const projectTypeSelect = document.getElementById('project_type');
          if (projectTypeSelect) {
            projectTypeSelect.addEventListener('change', function() {
              updateCategories(this.value);
            });
            
            if (projectTypeSelect.value) {
              updateCategories(projectTypeSelect.value);
            }
          }
          
          const categorySelect = document.getElementById('project_category');
          if (categorySelect && categorySelect.value) {
            updateSubcategories(categorySelect.value);
          }
          
          categorySelect.addEventListener('change', function() {
            updateSubcategories(this.value);
          });
        });

        document.addEventListener('DOMContentLoaded', function() {
          const projectTypeSelect = document.getElementById('project_project_type');
          const realEstateFields = document.getElementById('real-estate-fields');

          function toggleRealEstateFields() {
            if (projectTypeSelect.value === 'real_estate') {
              realEstateFields.style.display = 'flex';
            } else {
              realEstateFields.style.display = 'none';
            }
          }

          projectTypeSelect.addEventListener('change', toggleRealEstateFields);

          toggleRealEstateFields();
        });

        // Function to cleanup stuck uploads
        async function cleanupStuckUploads() {
          const button = document.querySelector('.cleanup-stuck-btn');
          const originalText = button.textContent;
          
          // Disable button and show loading state
          button.disabled = true;
          button.textContent = button.dataset.cleaningText;
          
          try {
            <% if @stuck_uploads&.any? %>
              <% @stuck_uploads.each do |upload| %>
                const response<%= upload.id %> = await fetch('/uploads/<%= upload.id %>/cleanup_stuck', {
                  method: 'POST',
                  headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                    'Content-Type': 'application/json'
                  }
                });
                
                if (!response<%= upload.id %>.ok) {
                  const errorData = await response<%= upload.id %>.json();
                  console.error('Cleanup failed for upload <%= upload.id %>:', errorData);
                }
              <% end %>
            <% end %>
            
            // Refresh the page to update the notification
            window.location.reload();
            
          } catch (error) {
            console.error('Cleanup failed:', error);
            // Restore button state
            button.disabled = false;
            button.textContent = originalText;
            alert(button.dataset.cleanupFailedText);
          }
        }

        // Multi-file deletion functions
        function toggleAllFiles(selectAllCheckbox) {
          const fileCheckboxes = document.querySelectorAll('.file-select-checkbox');
          fileCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
          });
          updateDeleteButton();
        }

        function updateDeleteButton() {
          const selectedFiles = document.querySelectorAll('.file-select-checkbox:checked');
          const deleteButton = document.getElementById('delete-selected-btn');
          const selectAllCheckbox = document.getElementById('select-all-files');
          
          // Enable/disable delete button based on selection
          deleteButton.disabled = selectedFiles.length === 0;
          
          // Update select all checkbox state
          const allFileCheckboxes = document.querySelectorAll('.file-select-checkbox');
          if (selectedFiles.length === allFileCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
          } else if (selectedFiles.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
          } else {
            selectAllCheckbox.indeterminate = true;
          }
        }

        async function deleteSelectedFiles() {
          const selectedFiles = document.querySelectorAll('.file-select-checkbox:checked');
          
          if (selectedFiles.length === 0) {
            alert('<%= j t('projects.form.no_files_selected', default: 'No files selected') %>');
            return;
          }
          
          const fileIds = Array.from(selectedFiles).map(checkbox => checkbox.value);
          const confirmMessage = '<%= j t('projects.form.delete_files_confirm', default: 'Are you sure you want to delete %{count} file(s)?') %>';
          
          if (!confirm(confirmMessage.replace('%{count}', selectedFiles.length))) {
            return;
          }
          
          const deleteButton = document.getElementById('delete-selected-btn');
          const originalText = deleteButton.textContent;
          
          // Disable button and show loading state
          deleteButton.disabled = true;
          deleteButton.textContent = '<%= j t('projects.form.deleting', default: 'Deleting...') %>';
          
          try {
            const response = await fetch('/projects/<%= @project.id %>/bulk_delete_files', {
              method: 'DELETE',
              headers: {
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ file_ids: fileIds })
            });
            
            if (response.ok) {
              // Refresh the page to update the file list
              window.location.reload();
            } else {
              const errorData = await response.json();
              alert('<%= j t('projects.form.delete_failed', default: 'Failed to delete files') %>: ' + (errorData.error || 'Unknown error'));
              // Restore button state
              deleteButton.disabled = false;
              deleteButton.textContent = originalText;
            }
          } catch (error) {
            console.error('Delete failed:', error);
            alert('<%= j t('projects.form.delete_failed', default: 'Failed to delete files') %>');
            // Restore button state
            deleteButton.disabled = false;
            deleteButton.textContent = originalText;
          }
        }

        // Sharing sidebar functionality
        function getSecurityLevel(audience, access) {
          if (audience === 'network' && access === 'summary') return 'very_high';
          if (audience === 'network' && access === 'full') return 'high';
          if (audience === 'all' && access === 'summary') return 'medium';
          return 'low';
        }

        function updateSecurityIndicator(audience, access) {
          const securityLevel = getSecurityLevel(audience, access);
          const indicator = document.getElementById('security-indicator');
          const dot = indicator.querySelector('.security-dot');
          const levelText = indicator.querySelector('.security-level-text');
          const description = indicator.querySelector('.security-description');

          // Remove existing classes
          indicator.classList.remove('security-very_high', 'security-high', 'security-medium', 'security-low');
          
          // Add new class and content
          indicator.classList.add(`security-${securityLevel}`);
          
          const levelLabels = {
            'very_high': '<%= j t("projects.form.share.very_secure", default: "Hidden Deal") %>',
            'high': '<%= j t("projects.form.share.secure", default: "Partially Hidden") %>',
            'medium': '<%= j t("projects.form.share.moderate", default: "Unlisters' Deal") %>',
            'low': '<%= j t("projects.form.share.open", default: "Open Deal") %>'
          };
          
          const descriptions = {
            'very_high': '<%= j t("projects.form.share.network_approval", default: "Network only, approval required") %>',
            'high': '<%= j t("projects.form.share.network_direct", default: "Network only, direct access") %>',
            'medium': '<%= j t("projects.form.share.public_approval", default: "Public listing, approval required") %>',
            'low': '<%= j t("projects.form.share.public_direct", default: "Public listing, direct access") %>'
          };

          levelText.textContent = levelLabels[securityLevel];
          description.textContent = descriptions[securityLevel];
        }

        function updateAudienceSelection(audience) {
          const networkBtn = document.querySelector('[data-audience="network"]');
          const allBtn = document.querySelector('[data-audience="all"]');
          const networkCheckbox = document.getElementById('project_network_only');
          const semiPublicCheckbox = document.getElementById('project_semi_public');

          // Update button states
          networkBtn.classList.toggle('active', audience === 'network');
          allBtn.classList.toggle('active', audience === 'all');

          // Update form fields
          networkCheckbox.checked = audience === 'network';
          semiPublicCheckbox.checked = audience === 'all';
        }

        function updateAccessSelection(access) {
          const summaryBtn = document.querySelector('[data-access="summary"]');
          const fullBtn = document.querySelector('[data-access="full"]');
          const summaryCheckbox = document.getElementById('project_summary_only');
          const fullAccessCheckbox = document.getElementById('project_full_access');

          // Don't allow selecting disabled button
          if (access === 'summary' && (summaryBtn.disabled || summaryBtn.classList.contains('disabled'))) {
            // Force selection to full if summary is disabled
            access = 'full';
          }

          // Update button states
          summaryBtn.classList.toggle('active', access === 'summary');
          fullBtn.classList.toggle('active', access === 'full');

          // Update form fields
          summaryCheckbox.checked = access === 'summary';
          fullAccessCheckbox.checked = access === 'full';
        }

        function getCurrentState() {
          const networkOnly = document.getElementById('project_network_only').checked;
          const semiPublic = document.getElementById('project_semi_public').checked;
          const summaryOnly = document.getElementById('project_summary_only').checked;
          const fullAccess = document.getElementById('project_full_access').checked;

          // Fix logic: prioritize semi_public for 'all' audience
          const audience = semiPublic ? 'all' : 'network';
          const access = fullAccess ? 'full' : 'summary';

          return { audience, access };
        }

        // COMMENTED OUT: JavaScript default setting logic - moved to Rails model for proper dirty tracking
        // function setNewProjectDefaults() {
        //   // Check if this is a new project by looking at the URL or form state
        //   const isNewProject = window.location.pathname.includes('/new') || 
        //                       (!document.getElementById('project_network_only').checked && 
        //                        !document.getElementById('project_semi_public').checked &&
        //                        !document.getElementById('project_summary_only').checked &&
        //                        !document.getElementById('project_full_access').checked);

        //   if (isNewProject) {
        //     // Set default values for new projects to match model defaults
        //     document.getElementById('project_summary_only').checked = false;   // NOT summary only (full access)
        //     document.getElementById('project_full_access').checked = true;     // FULL access (default)
        //     document.getElementById('project_network_only').checked = false;   // NOT network only (everyone)
        //     document.getElementById('project_semi_public').checked = true;     // SEMI PUBLIC (everyone - default)
        //     document.getElementById('project_project_status').checked = false; // DRAFT (default)
        //   }
        // }

        function updateStatusSelection(status) {
          const publishedBtn = document.querySelector('[data-status="published"]');
          const draftBtn = document.querySelector('[data-status="draft"]');
          const statusCheckbox = document.getElementById('project_project_status');

          // Update button states
          publishedBtn.classList.toggle('active', status === 'published');
          draftBtn.classList.toggle('active', status === 'draft');

          // Update form field - project_status true = published, false = draft
          statusCheckbox.checked = status === 'published';
        }

        function getCurrentStatus() {
          const statusCheckbox = document.getElementById('project_project_status');
          return statusCheckbox.checked ? 'published' : 'draft';
        }

        function initializeSharingSidebar() {
          // COMMENTED OUT: JavaScript default setting - moved to Rails model for proper dirty tracking
          // setNewProjectDefaults();
          
          const { audience, access } = getCurrentState();
          
          // Initialize visual state
          updateAudienceSelection(audience);
          updateAccessSelection(access);
          updateSecurityIndicator(audience, access);

          // Initialize status selection
          const status = getCurrentStatus();
          updateStatusSelection(status);

          // Add event listeners
          document.querySelectorAll('[data-audience]').forEach(button => {
            button.addEventListener('click', function() {
              const newAudience = this.dataset.audience;
              const { access } = getCurrentState();
              updateAudienceSelection(newAudience);
              updateSecurityIndicator(newAudience, access);
            });
          });

          document.querySelectorAll('[data-access]').forEach(button => {
            button.addEventListener('click', function(e) {
              // Prevent clicking disabled buttons
              if (this.disabled || this.classList.contains('disabled')) {
                e.preventDefault();
                e.stopPropagation();
                return false;
              }
              const newAccess = this.dataset.access;
              const { audience } = getCurrentState();
              updateAccessSelection(newAccess);
              updateSecurityIndicator(audience, newAccess);
            });
          });

          // Add status button listeners
          document.querySelectorAll('[data-status]').forEach(button => {
            button.addEventListener('click', function() {
              const newStatus = this.dataset.status;
              updateStatusSelection(newStatus);
            });
          });
        }

        // Initialize sharing sidebar when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
          if (document.querySelector('.sharing-sidebar')) {
            initializeSharingSidebar();
          }
        });

      </script>
     
    </div>

    <div class="side-right">

      <!-- Project Status -->
      <div class="side-box sharing-sidebar">
         <!-- Header -->
        <div class="sharing-header">
          <div class="sharing-header-content">
            <%= heroicon "document-text", variant: :outline, options: { class: "sharing-icon" } %>
            <h3><%= t('projects.form.project_status', default: 'Deal Status') %></h3>
          </div>
        </div>

        <!-- Draft Status Indicator -->
        <% if project.persisted? %>
          <div class="draft-status-bar">
            <% if project.draft? %>
              <div class="status-indicator draft">
                <div class="status-status">
                  <div class="status-dot"></div>
                  <span class="status-level-text">
                    <%= t('projects.form.draft', default: 'Draft') %>
                  </span>
                </div>
                <div class="status-description">
                  <%= t('projects.form.draft_description', default: 'This project is saved as a draft. Complete required fields to publish.') %>
                </div>
              </div>
            <% elsif project.published? && !project.approved? %>
              <div class="status-indicator pending">
                <div class="status-status">
                  <div class="status-dot"></div>
                  <span class="status-level-text">
                    <%= t('projects.form.pending_approval', default: 'Pending Approval') %>
                  </span>
                </div>
                <div class="status-description">
                  <%= t('projects.form.pending_description', default: 'Your project is submitted and awaiting admin approval.') %>
                </div>
              </div>
            <% elsif project.published? && project.approved? %>
              <div class="status-indicator published">
                <div class="status-status">
                  <div class="status-dot"></div>
                  <span class="status-level-text">
                    <%= t('projects.form.published', default: 'Published') %>
                  </span>
                </div>
                <div class="status-description">
                  <%= t('projects.form.published_description', default: 'Your project is live and visible to users.') %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>

        <!-- Sidebar Sharing and Visibility -->
        <div class="">
          <div class="selection-group">
            <!-- Hidden form field -->
            <%= form.check_box :project_status, checked: (@project.new_record? ? false : @project.project_status), style: "display: none;" %>
            <div class="selection-grid">
              <button type="button" class="selection-button" data-status="published">
                <%= heroicon "check-circle", variant: :outline, options: { class: "selection-icon" } %>
                <div class="selection-text"><%= t('projects.form.published', default: 'Published') %></div>
              </button>
              <button type="button" class="selection-button" data-status="draft">
                <%= heroicon "pencil-square", variant: :outline, options: { class: "selection-icon" } %>
                <div class="selection-text"><%= t('projects.form.draft', default: 'Draft') %></div>
              </button>
            </div>
          </div>
          <div class="mb-1">
            <div class="selection-label">
              <%= t('projects.form.last_update') %>:
              <%= l(project.updated_at, format: :short_dmy) if project.updated_at %>
            </div>
          </div>
        </div>
      </div>


      <div class="side-box sharing-sidebar">
        <!-- Header -->
        <div class="sharing-header">
          <div class="sharing-header-content">
            <%= heroicon "lock-closed", variant: :outline, options: { class: "sharing-icon" } %>
            <h3><%= t('projects.form.share.project_access', default: 'Deal Access') %></h3>
          </div>
        </div>
        
        <!-- Security Level Indicator -->
        <div id="security-indicator" class="security-indicator">
          <div class="security-status">
            <div class="security-dot"></div>
            <span class="security-level-text"></span>
          </div>
          <div class="security-description"></div>
        </div>

        <!-- Hidden form fields -->
        <%= form.check_box :summary_only, checked: (@project.new_record? ? false : @project.summary_only), style: "display: none;" %>
        <%= form.check_box :full_access, checked: (@project.new_record? ? true : @project.full_access), style: "display: none;" %>
        <%= form.check_box :network_only, checked: (@project.new_record? ? false : @project.network_only), style: "display: none;" %>
        <%= form.check_box :semi_public, checked: (@project.new_record? ? true : @project.semi_public), style: "display: none;" %>

        <!-- Access Level Selection -->
        <div class="selection-group">
          <div class="selection-label"><%= t('projects.form.share.who_can_discover', default: 'Who can discover this project?') %></div>
          <div class="selection-grid">
            <button type="button" class="selection-button" data-audience="network">
              <%= heroicon "users", variant: :outline, options: { class: "selection-icon" } %>
              <div class="selection-text"><%= t('projects.form.share.my_network') %></div>
            </button>
            <button type="button" class="selection-button" data-audience="all">
              <%= heroicon "user-group", variant: :outline, options: { class: "selection-icon" } %>
              <div class="selection-text"><%= t('projects.form.share.everyone') %></div>
            </button>
          </div>
        </div>

        <!-- Access Level Selection -->
        <div class="selection-group">
          <div class="selection-label"><%= t('projects.form.share.what_can_see', default: 'What can they see initially?') %></div>
          <div class="selection-grid">
            <% can_create_summary = allowed_to?(:can_create_summary_only?, project, with: ProjectPolicy) %>
            <button type="button" 
                    class="selection-button <%= 'disabled' unless can_create_summary %>" 
                    data-access="summary"
                    <%= 'disabled' unless can_create_summary %>>
              <%= heroicon "lock-closed", variant: :outline, options: { class: "selection-icon" } %>
              <div class="selection-text"><%= t('projects.form.share.title_only') %></div>
              <div class="selection-subtext">
                <% if can_create_summary %>
                  <%= t('projects.form.share.must_request', default: 'Must request details') %>
                <% else %>
                  <span class="upgrade-badge"><%= t('subscriptions.upgrade_required', default: 'standard') %></span>
                <% end %>
              </div>
            </button>
            <button type="button" class="selection-button" data-access="full">
              <%= heroicon "eye", variant: :outline, options: { class: "selection-icon" } %>
              <div class="selection-text"><%= t('projects.form.share.everything') %></div>
              <div class="selection-subtext"><%= t('projects.form.share.immediate_access', default: 'Immediate access') %></div>
            </button>
          </div>
        </div>
      </div>
      
    <% end %>
      
      <div class="side-right">
        <div class="actions">
          <%= button_to t('common.actions.delete'), @project, method: :delete, data: { confirm: t('projects.form.delete_confirm') }, class: "text-link red" %> |
          <%= link_to t('projects.form.preview'), @project %> 
          
          <!-- Single Save Action -->
          <div class="draft-actions">
            <button type="submit" form="project_form" class="button primary">
              <%= t('common.actions.save') %>
            </button>
          </div>
        </div>
      </div>
      
  </div>
</div>

<%= render partial: 'full_details_users', locals: { auths: @project_auths, reqs: @auth_requests } %>

<!-- Modal outside the form -->
<div id="fileModal" class="modal">
  <div class="modal-content">
    <span class="close-button" onclick="document.getElementById('fileModal').style.display='none'">&times;</span>
    
    <div class="main-box">
      <h3 class="text-l"><%= t('projects.form.attached_files') %></h3>
      
      <% if @project.persisted? && @project.private_files.attached? %>
        <div class="bulk-actions">
          <div class="bulk-controls">
            <label>
              <input type="checkbox" id="select-all-files" onchange="toggleAllFiles(this)">
              <%= t('projects.form.select_all', default: 'Select All') %>
            </label>
            <button type="button" id="delete-selected-btn" onclick="deleteSelectedFiles()" class="delete-button" disabled>
              <%= t('projects.form.delete_selected', default: 'Delete Selected') %>
            </button>
          </div>
        </div>
      <% end %>

      <ul class="file-list">
        <% if @project.persisted? && @project.private_files.attached? %>
          <% @project.private_files.each_with_index do |file, i| %>
            <li class="file-list-item">
              <div class="file-checkbox">
                <input type="checkbox" 
                       class="file-select-checkbox" 
                       value="<%= file.id %>" 
                       onchange="updateDeleteButton()">
              </div>
              <div class="file-info">
                <div class="file-name">
                  <%= link_to file.filename.to_s, secure_download_project_private_file_path(@project, file) if file.blob.present? %>
                </div>
                <div class="file-meta">
                  <span><%= number_to_human_size(file.blob.byte_size) %></span>
                  <span><%= file.created_at.strftime("%b %d %Y at %H:%M") %></span>
                </div>
              </div>
              <div class="file-actions">
                <%= button_to t('projects.form.delete_file'), destroy_file_project_path(@project, file_id: file.id),
                        method: :delete,
                        data: { confirm: t('projects.form.delete_file_confirm', default: "Are you sure?") }, class: "delete-file-button" %>
              </div>
            </li>
          <% end %>
        <% end %>
      </ul>
    </div>

  </div>
</div>