// ABOUTME: Styles for subscription-based feature gates and upgrade teasers
// ABOUTME: Handles disabled states and upgrade badges for free users

// Disabled states for buttons restricted to premium users
.selection-button.disabled,
.action-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  position: relative;
  background-color: #f3f4f6;
  
  &:hover {
    background-color: #f3f4f6;
    transform: none;
    box-shadow: none;
  }
  
  &:active {
    transform: none;
  }
}

// Upgrade required badge
.upgrade-badge {
  display: inline-block;
  background-color: #f59e0b;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-left: 8px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  
  // When inside a button subtext
  .selection-subtext & {
    margin-left: 0;
    display: block;
    margin-top: 4px;
  }
}

// Disabled selection button specific styles
.selection-button.disabled {
  .selection-icon {
    opacity: 0.5;
  }
  
  .selection-text {
    color: #9ca3af;
  }
  
  // Override the active state styles
  &.active {
    background-color: #f3f4f6;
    border-color: #d1d5db;
    
    .selection-icon,
    .selection-text {
      color: #9ca3af;
    }
  }
}