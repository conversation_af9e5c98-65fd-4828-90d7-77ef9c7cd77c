# ABOUTME: Admin controller for managing user subscriptions and tier assignments
# ABOUTME: Provides admin interface for viewing users by tier and manually updating subscription status
class Admin::SubscriptionsController < Admin::BaseController

  def index
    # Filter users based on status parameter
    @status_filter = params[:status]

    @users_query = case @status_filter
                   when 'free'
                     # Users with active free plan subscription or legacy free tier
                     User.left_joins(:active_subscription => :plan)
                          .where("plans.tier = ? OR (users.subscription_tier = ? AND subscriptions.id IS NULL)", 
                                 Plan.tiers['free'], User.subscription_tiers['free'])
                   when 'premium'
                     # Users with active premium plan subscription or legacy premium tier
                     User.left_joins(:active_subscription => :plan)
                          .where("plans.tier = ? OR (users.subscription_tier = ? AND subscriptions.id IS NULL)", 
                                 Plan.tiers['premium'], User.subscription_tiers['premium'])
                   when 'pilot'
                     # Users with active pilot plan subscription or legacy pilot tier
                     User.left_joins(:active_subscription => :plan)
                          .where("plans.tier = ? OR (users.subscription_tier = ? AND subscriptions.id IS NULL)", 
                                 Plan.tiers['pilot'], User.subscription_tiers['pilot'])
                   when 'approved'
                     User.approved
                   when 'pending'
                     User.where(approved: false)
                   when 'active_subscriptions'
                     User.joins(:active_subscription)
                   when 'expired_subscriptions'
                     User.joins(:subscriptions).where(subscriptions: { status: [:canceled, :unpaid, :incomplete_expired] })
                          .distinct
                   else
                     User.all
                   end

    @users_query = @users_query.includes(:user_profile, :active_subscription => :plan).order(created_at: :desc, id: :desc)
    @pagy, @users = pagy(@users_query, limit: 25)

    # Get counts for status summary
    @status_counts = {
      total: User.count,
      free: User.left_joins(:active_subscription => :plan)
                .where("plans.tier = ? OR (users.subscription_tier = ? AND subscriptions.id IS NULL)", 
                       Plan.tiers['free'], User.subscription_tiers['free']).count,
      premium: User.left_joins(:active_subscription => :plan)
                   .where("plans.tier = ? OR (users.subscription_tier = ? AND subscriptions.id IS NULL)", 
                          Plan.tiers['premium'], User.subscription_tiers['premium']).count,
      pilot: User.left_joins(:active_subscription => :plan)
                 .where("plans.tier = ? OR (users.subscription_tier = ? AND subscriptions.id IS NULL)", 
                        Plan.tiers['pilot'], User.subscription_tiers['pilot']).count,
      approved: User.approved.count,
      pending: User.where(approved: false).count,
      active_subscriptions: User.joins(:active_subscription).distinct.count,
      expired_subscriptions: User.joins(:subscriptions)
                                 .where(subscriptions: { status: [:canceled, :unpaid, :incomplete_expired] })
                                 .distinct.count
    }

    # Load available plans for the subscription form
    @available_plans = Plan.active.order(:tier, :interval)
  end
  
  def show
    @user = User.find(params[:id])
    @subscriptions = @user.subscriptions.includes(:plan).order(created_at: :desc)
    @available_plans = Plan.active.order(:tier, :interval)
  end
  
  def create_subscription
    @user = User.find(params[:id])
    @plan = Plan.find(params[:plan_id])
    
    # Cancel any active subscription first
    if @user.active_subscription.present?
      @user.active_subscription.cancel!(reason: "Admin created new subscription")
    end
    
    # Create new subscription
    subscription = @user.subscriptions.build(
      plan: @plan,
      status: :active,
      payment_provider: :manual,
      current_period_start: Time.current,
      current_period_end: calculate_period_end(@plan)
    )
    
    if subscription.save
      redirect_to admin_subscription_path(@user), 
                  notice: "Subscription created successfully for #{@user.email}"
    else
      redirect_to admin_subscription_path(@user), 
                  alert: "Failed to create subscription: #{subscription.errors.full_messages.join(', ')}"
    end
  end
  
  def cancel_subscription
    @user = User.find(params[:id])
    @subscription = @user.subscriptions.find(params[:subscription_id])
    
    @subscription.cancel!(
      reason: params[:cancel_reason] || "Admin canceled",
      at_period_end: params[:at_period_end] != 'false'
    )
    
    redirect_to admin_subscription_path(@user), 
                notice: "Subscription canceled successfully"
  end
  
  def reactivate_subscription
    @user = User.find(params[:id])
    @subscription = @user.subscriptions.find(params[:subscription_id])
    
    if @subscription.reactivate!
      redirect_to admin_subscription_path(@user), 
                  notice: "Subscription reactivated successfully"
    else
      redirect_to admin_subscription_path(@user), 
                  alert: "Cannot reactivate this subscription"
    end
  end
  
  def update_tier
    @user = User.find(params[:id])
    
    # Legacy support - update user tier attributes
    if @user.update(tier_params)
      # Also create a subscription record if using new system
      if params[:create_subscription] == 'true' && params[:plan_id].present?
        plan = Plan.find(params[:plan_id])
        create_subscription_for_user(@user, plan)
      end
      
      redirect_to admin_subscriptions_path, notice: "Tier updated successfully for #{@user.email}"
    else
      redirect_to admin_subscriptions_path, alert: "Failed to update tier: #{@user.errors.full_messages.join(', ')}"
    end
  end
  
  def update_approval
    @user = User.find(params[:id])
    action = params[:approval_action]
    
    begin
      case action
      when 'approve'
        @user.approve!
        redirect_to admin_subscriptions_path, notice: "User #{@user.email} has been approved successfully."
      when 'disapprove'
        @user.disapprove!
        redirect_to admin_subscriptions_path, notice: "User #{@user.email} has been disapproved."
      else
        redirect_to admin_subscriptions_path, alert: "Invalid approval action."
      end
    rescue => e
      redirect_to admin_subscriptions_path, alert: "Failed to update approval status: #{e.message}"
    end
  end

  def destroy
    @user = User.find(params[:id])
    
    # Prevent admin from deleting themselves
    if @user == current_user
      redirect_to admin_subscriptions_path, alert: "Cannot delete your own account"
      return
    end
    
    # Require confirmation
    required_confirmation = "DELETE #{@user.email}"
    provided_confirmation = params[:confirm_deletion]
    
    if provided_confirmation.blank?
      redirect_to admin_subscriptions_path, alert: "Deletion cancelled - confirmation required"
      return
    end
    
    if provided_confirmation != required_confirmation
      redirect_to admin_subscriptions_path, alert: "Confirmation text does not match. Expected: '#{required_confirmation}'"
      return
    end
    
    begin
      user_email = @user.email
      @user.destroy!
      redirect_to admin_subscriptions_path, notice: "User #{user_email} has been deleted successfully along with all associated data (projects, wants, connections, profile)"
    rescue => e
      redirect_to admin_subscriptions_path, alert: "Failed to delete user: #{e.message}"
    end
  end
  
  private
  
  def tier_params
    params.permit(:subscription_tier, :subscription_expires_at)
  end
  
  def calculate_period_end(plan)
    case plan.interval
    when 'month'
      1.month.from_now
    when 'year'
      1.year.from_now
    when 'one_time'
      100.years.from_now
    else
      1.month.from_now
    end
  end
  
  def create_subscription_for_user(user, plan)
    # Cancel existing active subscription if any
    user.active_subscription&.cancel!(reason: "Admin created new subscription")
    
    # Create new subscription
    user.subscriptions.create!(
      plan: plan,
      status: :active,
      payment_provider: :manual,
      current_period_start: Time.current,
      current_period_end: calculate_period_end(plan)
    )
  end
end