class ProjectPolicy < ApplicationPolicy
  
  # ApplicationPolicy uses a user object for authorization purposes
  # Policies are to be described clearly 
  # Use granular methods to define permissions\
  # False negative is better than false positive in private show and files access
  
  def index?
    # Everyone can see index, but what they see is controlled in scopes
    true
  end

  def show?
    # Can see full details if:
    # 1. Owner of project (even if not approved)
    # 2. Has explicit ProjectAuth AND project is approved
    #   ProjectAuth has 4 levels: no_access, pending, summary, full. 
    #   Only full allows any access to private resources
    return true if record.user_id == user.id
    return false unless record.approved?
    
    # Check if free user can view based on age restriction
    return false unless viewable_by_free_user?
    
    record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
  end

  #TODO: Both policies are identical. Check if needed both
  def view_full_details?
    # Owner always has access (even if not approved)
    return true if record.user_id == user.id
    
    # Project must be approved for any non-owner access
    return false unless record.approved?
    
    # Check if free user can view based on age restriction
    return false unless viewable_by_free_user?
    
    # Explicit ProjectAuth grants (existing behavior)
    return true if record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
    
    # NEW: Honor full_access setting for automatic access
    if record.full_access?
      if record.semi_public?
        return true  # All authenticated users get access
      elsif record.network_only?
        return user_connected_to_project_owner?
      end
    end
    
    false
  end

  # def access_files?
  #   # 1. Owner of project
  #   # 2. Has explicit ProjectAuth.
  #   record.user_id == user.id || 
  #     record.project_auths.exists?(user_id: user.id, access_level: 'full_details')
  # end

  def grant_full_details?
    manage_access?
  end

  def deny_access?
    manage_access?
  end

  def delete_access?
    manage_access?
  end

  def manage_access?
    # Ensures only the project owner can approve or deny access
    record.user_id == user.id
  end

  def create?
    # Any authenticated user can create projects
    true
  end

  def upload_files?
    # Any authenticated user can upload files
    true
  end

  def edit?
    # Only owner of project can edit
    record.user_id == user.id
  end

  def update?
    # Only owner of project can update
    record.user_id == user.id
  end

  def destroy?
    # Only owner of project can destroy
    record.user_id == user.id
  end

  def destroy_file?
    # Only owner of project can destroy
    record.user_id == user.id
  end

  def bulk_delete_files?
    # Only owner of project can bulk delete files
    record.user_id == user.id
  end

  # Check if free user can view this project (14-day restriction)
  def viewable_by_free_user?
    return true if user.active_subscription? # Subscribed users can view all projects
    return true if record.user_id == user.id # Owner can always view their own
    
    # Free users can only see projects older than 14 days
    if record.first_published_at.present?
      record.first_published_at <= 14.days.ago
    else
      # Fallback to created_at if first_published_at not set
      record.created_at <= 14.days.ago
    end
  end

  # Check if user can create summary_only projects
  def can_create_summary_only?
    # Free users cannot create summary_only projects
    user.active_subscription?
  end

  # Check if user can request access to projects
  def can_request_access?
    # Free users cannot request access
    user.active_subscription?
  end

  private

  def user_connected_to_project_owner?
    NetworkConnection.where(
      '(inviter_id = ? AND invitee_id = ?) OR (inviter_id = ? AND invitee_id = ?)',
      user.id, record.user_id, record.user_id, user.id
    ).exists?
  end

end